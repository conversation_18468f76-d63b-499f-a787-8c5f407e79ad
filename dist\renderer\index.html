<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>मैथिली विकास कोष Shop Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 30px;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        
        .features {
            text-align: left;
            margin: 30px 0;
        }
        
        .features h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }
        
        .version {
            margin-top: 30px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">मैथिली विकास कोष</div>
        <div class="subtitle">Shop Management System</div>
        
        <div class="status">
            ✅ Electron Application Successfully Initialized!
        </div>
        
        <div class="features">
            <h3>Phase 1 Complete:</h3>
            <ul>
                <li>Electron project with TypeScript setup</li>
                <li>Project folder structure established</li>
                <li>Basic main process and preload script</li>
                <li>TypeScript compilation working</li>
                <li>Security configurations in place</li>
            </ul>
        </div>
        
        <div class="features">
            <h3>Next Steps (Phase 2):</h3>
            <ul>
                <li>SQLite database setup</li>
                <li>Database schema design</li>
                <li>Migration system</li>
                <li>Sample data seeding</li>
            </ul>
        </div>
        
        <button class="btn" onclick="testElectronAPI()">Test Electron API</button>
        <button class="btn" onclick="showAppInfo()">Show App Info</button>
        
        <div class="version" id="version-info">
            Loading version information...
        </div>
    </div>

    <script>
        // Test Electron API functionality
        async function testElectronAPI() {
            if (window.electronAPI) {
                alert('✅ Electron API is working correctly!');
            } else {
                alert('❌ Electron API not available');
            }
        }
        
        // Show app information
        async function showAppInfo() {
            try {
                if (window.electronAPI && window.electronAPI.getAppVersion) {
                    const version = await window.electronAPI.getAppVersion();
                    alert(`App Version: ${version}`);
                } else {
                    alert('App version API not available yet');
                }
            } catch (error) {
                alert('Error getting app version: ' + error.message);
            }
        }
        
        // Load version info on startup
        document.addEventListener('DOMContentLoaded', async () => {
            const versionElement = document.getElementById('version-info');
            try {
                if (window.electronAPI && window.electronAPI.getAppVersion) {
                    const version = await window.electronAPI.getAppVersion();
                    versionElement.textContent = `Application Version: ${version}`;
                } else {
                    versionElement.textContent = 'Version: 1.0.0 (Development)';
                }
            } catch (error) {
                versionElement.textContent = 'Version: 1.0.0 (Development)';
            }
        });
    </script>
</body>
</html>
