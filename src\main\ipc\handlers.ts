import { ipc<PERSON>ain, app, BrowserWindow, dialog, Notification } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Register all IPC handlers for the main process
 */
export function registerIpcHandlers(): void {
  // App-related handlers
  ipcMain.handle('app:getVersion', () => {
    return app.getVersion();
  });

  // Window control handlers
  ipcMain.handle('window:minimize', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      window.minimize();
    }
  });

  ipcMain.handle('window:maximize', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      if (window.isMaximized()) {
        window.unmaximize();
      } else {
        window.maximize();
      }
    }
  });

  ipcMain.handle('window:close', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window) {
      window.close();
    }
  });

  // File operation handlers
  ipcMain.handle('files:selectImage', async () => {
    const result = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] }
      ]
    });

    if (!result.canceled && result.filePaths.length > 0) {
      return result.filePaths[0];
    }
    return null;
  });

  ipcMain.handle('files:saveImage', async (_event, imageData: Buffer, filename: string) => {
    try {
      const userDataPath = app.getPath('userData');
      const imagesDir = path.join(userDataPath, 'images');
      
      // Create images directory if it doesn't exist
      if (!fs.existsSync(imagesDir)) {
        fs.mkdirSync(imagesDir, { recursive: true });
      }

      const filePath = path.join(imagesDir, filename);
      fs.writeFileSync(filePath, imageData);
      
      return filePath;
    } catch (error) {
      console.error('Error saving image:', error);
      throw error;
    }
  });

  // Notification handlers
  ipcMain.handle('notifications:show', (_event, title: string, body: string) => {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title,
        body,
        icon: path.join(__dirname, '../../../assets/icons/app-icon.png')
      });
      
      notification.show();
      
      notification.on('click', () => {
        const window = BrowserWindow.getFocusedWindow();
        if (window) {
          if (window.isMinimized()) {
            window.restore();
          }
          window.focus();
        }
      });
    }
  });

  // Database handlers (placeholders for now - will be implemented in Phase 2)
  ipcMain.handle('db:getUsers', async () => {
    // TODO: Implement database query
    return [
      { id: 1, name: 'Admin User', role: 'admin', email: '<EMAIL>' },
      { id: 2, name: 'Staff User', role: 'staff', email: '<EMAIL>' }
    ];
  });

  ipcMain.handle('db:createUser', async (_event, userData: any) => {
    // TODO: Implement database insert
    console.log('Creating user:', userData);
    return { id: Date.now(), ...userData };
  });

  ipcMain.handle('db:getProducts', async () => {
    // TODO: Implement database query
    return [
      { 
        id: 1, 
        name: 'Mithila Painting - Madhubani Art', 
        category: 'Paintings', 
        price: 2500, 
        stock: 5,
        artist: 'Sita Devi'
      },
      { 
        id: 2, 
        name: 'Traditional Mithila Saree', 
        category: 'Sarees', 
        price: 3500, 
        stock: 3,
        artist: 'Ganga Devi'
      },
      { 
        id: 3, 
        name: 'Handcrafted Mithila Bag', 
        category: 'Bags', 
        price: 1200, 
        stock: 8,
        artist: 'Radha Devi'
      }
    ];
  });

  ipcMain.handle('db:createProduct', async (_event, productData: any) => {
    // TODO: Implement database insert
    console.log('Creating product:', productData);
    return { id: Date.now(), ...productData };
  });

  ipcMain.handle('db:getCustomers', async () => {
    // TODO: Implement database query
    return [
      { 
        id: 1, 
        name: 'Rajesh Kumar', 
        email: '<EMAIL>', 
        phone: '+91-9876543210',
        address: 'Darbhanga, Bihar'
      },
      { 
        id: 2, 
        name: 'Priya Sharma', 
        email: '<EMAIL>', 
        phone: '+91-9876543211',
        address: 'Madhubani, Bihar'
      }
    ];
  });

  ipcMain.handle('db:createCustomer', async (_event, customerData: any) => {
    // TODO: Implement database insert
    console.log('Creating customer:', customerData);
    return { id: Date.now(), ...customerData };
  });

  ipcMain.handle('db:getOrders', async () => {
    // TODO: Implement database query
    return [
      { 
        id: 1, 
        customerId: 1, 
        customerName: 'Rajesh Kumar',
        total: 5000, 
        status: 'completed',
        date: '2024-12-15',
        items: [
          { productId: 1, productName: 'Mithila Painting - Madhubani Art', quantity: 2, price: 2500 }
        ]
      }
    ];
  });

  ipcMain.handle('db:createOrder', async (_event, orderData: any) => {
    // TODO: Implement database insert
    console.log('Creating order:', orderData);
    return { id: Date.now(), ...orderData };
  });

  console.log('✅ All IPC handlers registered successfully');
}
