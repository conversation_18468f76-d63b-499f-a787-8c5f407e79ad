/**
 * Database entity interfaces for Maithili Vikas Kosh Shop Management System
 */

// Base interface for all database entities
export interface BaseEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
}

// User Management
export interface User extends BaseEntity {
  username: string;
  email: string;
  passwordHash: string;
  salt: string;
  role: 'admin' | 'staff' | 'viewer';
  fullName: string;
  phone?: string;
  isActive: boolean;
  lastLoginAt?: string;
}

// Category Management
export interface Category extends BaseEntity {
  name: string;
  nameHindi: string;
  description?: string;
  descriptionHindi?: string;
  parentId?: number;
  sortOrder: number;
  isActive: boolean;
}

// Artist Management
export interface Artist extends BaseEntity {
  name: string;
  nameHindi: string;
  email?: string;
  phone?: string;
  address?: string;
  addressHindi?: string;
  specialization: string; // e.g., "Madhubani Painting", "Mithila Saree"
  specializationHindi: string;
  commissionRate: number; // Percentage
  bankAccount?: string;
  panNumber?: string;
  aadharNumber?: string;
  isActive: boolean;
  notes?: string;
}

// Product Management
export interface Product extends BaseEntity {
  name: string;
  nameHindi: string;
  description?: string;
  descriptionHindi?: string;
  categoryId: number;
  artistId: number;
  sku: string;
  barcode?: string;
  costPrice: number;
  sellingPrice: number;
  mrp: number;
  weight?: number; // in grams
  dimensions?: string; // e.g., "30x40 cm"
  materials?: string; // e.g., "Canvas, Acrylic Paint"
  materialsHindi?: string;
  colors?: string; // e.g., "Red, Blue, Yellow"
  isActive: boolean;
  tags?: string; // Comma-separated tags
  images?: string; // JSON array of image paths
}

// Inventory Management
export interface Inventory extends BaseEntity {
  productId: number;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  minStockLevel: number;
  maxStockLevel: number;
  reorderPoint: number;
  lastStockUpdate: string;
  location?: string; // Storage location
}

// Inventory Movement/History
export interface InventoryMovement extends BaseEntity {
  productId: number;
  movementType: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  referenceType?: 'order' | 'purchase' | 'adjustment' | 'return';
  referenceId?: number;
  userId: number;
  notes?: string;
}

// Customer Management
export interface Customer extends BaseEntity {
  customerCode: string;
  name: string;
  nameHindi?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address: string;
  addressHindi?: string;
  city: string;
  state: string;
  pincode: string;
  country: string;
  gstNumber?: string;
  customerType: 'individual' | 'business' | 'reseller';
  loyaltyPoints: number;
  totalPurchases: number;
  lastPurchaseDate?: string;
  isActive: boolean;
  notes?: string;
  preferences?: string; // JSON object for customer preferences
}

// Order Management
export interface Order extends BaseEntity {
  orderNumber: string;
  customerId: number;
  orderDate: string;
  expectedDeliveryDate?: string;
  actualDeliveryDate?: string;
  status: 'draft' | 'confirmed' | 'processing' | 'packed' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  shippingAmount: number;
  totalAmount: number;
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
  paymentMethod?: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque';
  shippingAddress: string;
  billingAddress: string;
  notes?: string;
  userId: number; // User who created the order
}

// Order Items
export interface OrderItem extends BaseEntity {
  orderId: number;
  productId: number;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discountAmount: number;
  taxAmount: number;
  notes?: string;
}

// Payment Management
export interface Payment extends BaseEntity {
  orderId: number;
  paymentDate: string;
  amount: number;
  paymentMethod: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'cancelled';
  transactionId?: string;
  referenceNumber?: string;
  bankName?: string;
  chequeNumber?: string;
  chequeDate?: string;
  notes?: string;
  userId: number;
}

// Supplier Management (for future use)
export interface Supplier extends BaseEntity {
  name: string;
  contactPerson: string;
  email?: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  gstNumber?: string;
  panNumber?: string;
  bankAccount?: string;
  paymentTerms?: string;
  isActive: boolean;
  notes?: string;
}

// Database Configuration
export interface DatabaseConfig {
  version: number;
  lastMigration: string;
  encryptionEnabled: boolean;
  backupEnabled: boolean;
  lastBackup?: string;
}

// Query result types
export interface QueryResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  rowsAffected?: number;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  category?: number;
  artist?: number;
  priceMin?: number;
  priceMax?: number;
  isActive?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}
