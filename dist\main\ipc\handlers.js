"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerIpcHandlers = registerIpcHandlers;
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
/**
 * Register all IPC handlers for the main process
 */
function registerIpcHandlers() {
    // App-related handlers
    electron_1.ipcMain.handle('app:getVersion', () => {
        return electron_1.app.getVersion();
    });
    // Window control handlers
    electron_1.ipcMain.handle('window:minimize', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            window.minimize();
        }
    });
    electron_1.ipcMain.handle('window:maximize', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            if (window.isMaximized()) {
                window.unmaximize();
            }
            else {
                window.maximize();
            }
        }
    });
    electron_1.ipcMain.handle('window:close', () => {
        const window = electron_1.BrowserWindow.getFocusedWindow();
        if (window) {
            window.close();
        }
    });
    // File operation handlers
    electron_1.ipcMain.handle('files:selectImage', async () => {
        const result = await electron_1.dialog.showOpenDialog({
            properties: ['openFile'],
            filters: [
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] }
            ]
        });
        if (!result.canceled && result.filePaths.length > 0) {
            return result.filePaths[0];
        }
        return null;
    });
    electron_1.ipcMain.handle('files:saveImage', async (_event, imageData, filename) => {
        try {
            const userDataPath = electron_1.app.getPath('userData');
            const imagesDir = path.join(userDataPath, 'images');
            // Create images directory if it doesn't exist
            if (!fs.existsSync(imagesDir)) {
                fs.mkdirSync(imagesDir, { recursive: true });
            }
            const filePath = path.join(imagesDir, filename);
            fs.writeFileSync(filePath, imageData);
            return filePath;
        }
        catch (error) {
            console.error('Error saving image:', error);
            throw error;
        }
    });
    // Notification handlers
    electron_1.ipcMain.handle('notifications:show', (_event, title, body) => {
        if (electron_1.Notification.isSupported()) {
            const notification = new electron_1.Notification({
                title,
                body,
                icon: path.join(__dirname, '../../../assets/icons/app-icon.png')
            });
            notification.show();
            notification.on('click', () => {
                const window = electron_1.BrowserWindow.getFocusedWindow();
                if (window) {
                    if (window.isMinimized()) {
                        window.restore();
                    }
                    window.focus();
                }
            });
        }
    });
    // Database handlers (placeholders for now - will be implemented in Phase 2)
    electron_1.ipcMain.handle('db:getUsers', async () => {
        // TODO: Implement database query
        return [
            { id: 1, name: 'Admin User', role: 'admin', email: '<EMAIL>' },
            { id: 2, name: 'Staff User', role: 'staff', email: '<EMAIL>' }
        ];
    });
    electron_1.ipcMain.handle('db:createUser', async (_event, userData) => {
        // TODO: Implement database insert
        console.log('Creating user:', userData);
        return { id: Date.now(), ...userData };
    });
    electron_1.ipcMain.handle('db:getProducts', async () => {
        // TODO: Implement database query
        return [
            {
                id: 1,
                name: 'Mithila Painting - Madhubani Art',
                category: 'Paintings',
                price: 2500,
                stock: 5,
                artist: 'Sita Devi'
            },
            {
                id: 2,
                name: 'Traditional Mithila Saree',
                category: 'Sarees',
                price: 3500,
                stock: 3,
                artist: 'Ganga Devi'
            },
            {
                id: 3,
                name: 'Handcrafted Mithila Bag',
                category: 'Bags',
                price: 1200,
                stock: 8,
                artist: 'Radha Devi'
            }
        ];
    });
    electron_1.ipcMain.handle('db:createProduct', async (_event, productData) => {
        // TODO: Implement database insert
        console.log('Creating product:', productData);
        return { id: Date.now(), ...productData };
    });
    electron_1.ipcMain.handle('db:getCustomers', async () => {
        // TODO: Implement database query
        return [
            {
                id: 1,
                name: 'Rajesh Kumar',
                email: '<EMAIL>',
                phone: '+91-9876543210',
                address: 'Darbhanga, Bihar'
            },
            {
                id: 2,
                name: 'Priya Sharma',
                email: '<EMAIL>',
                phone: '+91-9876543211',
                address: 'Madhubani, Bihar'
            }
        ];
    });
    electron_1.ipcMain.handle('db:createCustomer', async (_event, customerData) => {
        // TODO: Implement database insert
        console.log('Creating customer:', customerData);
        return { id: Date.now(), ...customerData };
    });
    electron_1.ipcMain.handle('db:getOrders', async () => {
        // TODO: Implement database query
        return [
            {
                id: 1,
                customerId: 1,
                customerName: 'Rajesh Kumar',
                total: 5000,
                status: 'completed',
                date: '2024-12-15',
                items: [
                    { productId: 1, productName: 'Mithila Painting - Madhubani Art', quantity: 2, price: 2500 }
                ]
            }
        ];
    });
    electron_1.ipcMain.handle('db:createOrder', async (_event, orderData) => {
        // TODO: Implement database insert
        console.log('Creating order:', orderData);
        return { id: Date.now(), ...orderData };
    });
    console.log('✅ All IPC handlers registered successfully');
}
//# sourceMappingURL=handlers.js.map