import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  
  // Database operations (to be implemented)
  database: {
    // User operations
    getUsers: () => ipcRenderer.invoke('db:getUsers'),
    createUser: (userData: any) => ipcRenderer.invoke('db:createUser', userData),
    
    // Product operations
    getProducts: () => ipcRenderer.invoke('db:getProducts'),
    createProduct: (productData: any) => ipcRenderer.invoke('db:createProduct', productData),
    
    // Customer operations
    getCustomers: () => ipcRenderer.invoke('db:getCustomers'),
    createCustomer: (customerData: any) => ipcRenderer.invoke('db:createCustomer', customerData),
    
    // Order operations
    getOrders: () => ipcRenderer.invoke('db:getOrders'),
    createOrder: (orderData: any) => ipcRenderer.invoke('db:createOrder', orderData),
  },
  
  // File operations
  files: {
    selectImage: () => ipcRenderer.invoke('files:selectImage'),
    saveImage: (imageData: any, filename: string) => ipcRenderer.invoke('files:saveImage', imageData, filename),
  },
  
  // Notifications
  notifications: {
    show: (title: string, body: string) => ipcRenderer.invoke('notifications:show', title, body),
  },
  
  // Event listeners
  on: (channel: string, callback: Function) => {
    const validChannels = [
      'app:update-available',
      'app:update-downloaded',
      'database:error',
      'notification:clicked'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    }
  },
  
  // Remove event listeners
  removeAllListeners: (channel: string) => {
    const validChannels = [
      'app:update-available',
      'app:update-downloaded',
      'database:error',
      'notification:clicked'
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.removeAllListeners(channel);
    }
  }
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      getAppVersion: () => Promise<string>;
      minimizeWindow: () => Promise<void>;
      maximizeWindow: () => Promise<void>;
      closeWindow: () => Promise<void>;
      database: {
        getUsers: () => Promise<any[]>;
        createUser: (userData: any) => Promise<any>;
        getProducts: () => Promise<any[]>;
        createProduct: (productData: any) => Promise<any>;
        getCustomers: () => Promise<any[]>;
        createCustomer: (customerData: any) => Promise<any>;
        getOrders: () => Promise<any[]>;
        createOrder: (orderData: any) => Promise<any>;
      };
      files: {
        selectImage: () => Promise<string>;
        saveImage: (imageData: any, filename: string) => Promise<string>;
      };
      notifications: {
        show: (title: string, body: string) => Promise<void>;
      };
      on: (channel: string, callback: Function) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}
