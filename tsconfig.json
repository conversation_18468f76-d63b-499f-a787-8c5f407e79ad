{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@main/*": ["main/*"], "@renderer/*": ["renderer/*"], "@shared/*": ["shared/*"], "@preload/*": ["preload/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build", "tests"]}