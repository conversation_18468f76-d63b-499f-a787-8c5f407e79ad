declare global {
    interface Window {
        electronAPI: {
            getAppVersion: () => Promise<string>;
            minimizeWindow: () => Promise<void>;
            maximizeWindow: () => Promise<void>;
            closeWindow: () => Promise<void>;
            database: {
                getUsers: () => Promise<any[]>;
                createUser: (userData: any) => Promise<any>;
                getProducts: () => Promise<any[]>;
                createProduct: (productData: any) => Promise<any>;
                getCustomers: () => Promise<any[]>;
                createCustomer: (customerData: any) => Promise<any>;
                getOrders: () => Promise<any[]>;
                createOrder: (orderData: any) => Promise<any>;
            };
            files: {
                selectImage: () => Promise<string>;
                saveImage: (imageData: any, filename: string) => Promise<string>;
            };
            notifications: {
                show: (title: string, body: string) => Promise<void>;
            };
            on: (channel: string, callback: Function) => void;
            removeAllListeners: (channel: string) => void;
        };
    }
}
export {};
//# sourceMappingURL=preload.d.ts.map