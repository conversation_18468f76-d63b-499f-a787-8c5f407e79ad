{"version": 3, "file": "handlers.js", "sourceRoot": "", "sources": ["../../../src/main/ipc/handlers.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,kDA+LC;AAtMD,uCAA6E;AAC7E,2CAA6B;AAC7B,uCAAyB;AAEzB;;GAEG;AACH,SAAgB,mBAAmB;IACjC,uBAAuB;IACvB,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE;QACpC,OAAO,cAAG,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACrC,MAAM,MAAM,GAAG,wBAAa,CAAC,gBAAgB,EAAE,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACrC,MAAM,MAAM,GAAG,wBAAa,CAAC,gBAAgB,EAAE,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;gBACzB,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,EAAE;QAClC,MAAM,MAAM,GAAG,wBAAa,CAAC,gBAAgB,EAAE,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,kBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,MAAM,GAAG,MAAM,iBAAM,CAAC,cAAc,CAAC;YACzC,UAAU,EAAE,CAAC,UAAU,CAAC;YACxB,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE;aAC7E;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAiB,EAAE,QAAgB,EAAE,EAAE;QACtF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,cAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAEpD,8CAA8C;YAC9C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAChD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAEtC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,kBAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,KAAa,EAAE,IAAY,EAAE,EAAE;QAC3E,IAAI,uBAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,IAAI,uBAAY,CAAC;gBACpC,KAAK;gBACL,IAAI;gBACJ,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oCAAoC,CAAC;aACjE,CAAC,CAAC;YAEH,YAAY,CAAC,IAAI,EAAE,CAAC;YAEpB,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC5B,MAAM,MAAM,GAAG,wBAAa,CAAC,gBAAgB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;wBACzB,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,CAAC;oBACD,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,4EAA4E;IAC5E,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;QACvC,iCAAiC;QACjC,OAAO;YACL,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,yBAAyB,EAAE;YAC9E,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,yBAAyB,EAAE;SAC/E,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,QAAa,EAAE,EAAE;QAC9D,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;QACxC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,QAAQ,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QAC1C,iCAAiC;QACjC,OAAO;YACL;gBACE,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,kCAAkC;gBACxC,QAAQ,EAAE,WAAW;gBACrB,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,WAAW;aACpB;YACD;gBACE,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,2BAA2B;gBACjC,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,YAAY;aACrB;YACD;gBACE,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,yBAAyB;gBAC/B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,YAAY;aACrB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAgB,EAAE,EAAE;QACpE,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QAC9C,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,WAAW,EAAE,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAC3C,iCAAiC;QACjC,OAAO;YACL;gBACE,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,oBAAoB;gBAC3B,KAAK,EAAE,gBAAgB;gBACvB,OAAO,EAAE,kBAAkB;aAC5B;YACD;gBACE,EAAE,EAAE,CAAC;gBACL,IAAI,EAAE,cAAc;gBACpB,KAAK,EAAE,mBAAmB;gBAC1B,KAAK,EAAE,gBAAgB;gBACvB,OAAO,EAAE,kBAAkB;aAC5B;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAiB,EAAE,EAAE;QACtE,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAChD,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,YAAY,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QACxC,iCAAiC;QACjC,OAAO;YACL;gBACE,EAAE,EAAE,CAAC;gBACL,UAAU,EAAE,CAAC;gBACb,YAAY,EAAE,cAAc;gBAC5B,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,WAAW;gBACnB,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE;oBACL,EAAE,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;iBAC5F;aACF;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAc,EAAE,EAAE;QAChE,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAC1C,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,SAAS,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;AAC5D,CAAC"}