import Database from 'better-sqlite3';
import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';
import { QueryR<PERSON>ult, PaginatedResult, SearchFilters, SortOptions } from '../../shared/types/database';

/**
 * Database service for Maithili Vikas Kosh Shop Management System
 * Uses better-sqlite3 for high performance SQLite operations
 */
export class DatabaseService {
  private db: Database.Database | null = null;
  private dbPath: string;
  private schemaPath: string;
  private isInitialized = false;

  constructor() {
    // Set database path in user data directory
    const userDataPath = app.getPath('userData');
    this.dbPath = path.join(userDataPath, 'maithili-shop.db');
    this.schemaPath = path.join(__dirname, 'schema.sql');
    
    console.log('Database path:', this.dbPath);
    console.log('Schema path:', this.schemaPath);
  }

  /**
   * Initialize the database connection and run migrations
   */
  async initialize(): Promise<void> {
    try {
      // Ensure user data directory exists
      const userDataPath = path.dirname(this.dbPath);
      if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true });
      }

      // Open database connection
      this.db = new Database(this.dbPath);
      
      // Enable WAL mode for better performance
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('foreign_keys = ON');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000');
      this.db.pragma('temp_store = MEMORY');

      // Run initial migration
      await this.runMigrations();
      
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    try {
      // Check if schema file exists
      if (!fs.existsSync(this.schemaPath)) {
        throw new Error(`Schema file not found: ${this.schemaPath}`);
      }

      // Read and execute schema
      const schema = fs.readFileSync(this.schemaPath, 'utf8');
      this.db!.exec(schema);

      // Initialize database config if not exists
      const configExists = this.db!.prepare(
        'SELECT COUNT(*) as count FROM database_config'
      ).get() as { count: number };

      if (configExists.count === 0) {
        this.db!.prepare(`
          INSERT INTO database_config (version, last_migration, encryption_enabled, backup_enabled)
          VALUES (1, datetime('now'), 0, 1)
        `).run();
      }

      console.log('✅ Database migrations completed');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Get database connection (ensure initialized)
   */
  private getDb(): Database.Database {
    if (!this.db || !this.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  /**
   * Execute a query and return results
   */
  query<T = any>(sql: string, params: any[] = []): QueryResult<T[]> {
    try {
      const db = this.getDb();
      const stmt = db.prepare(sql);
      const result = stmt.all(...params) as T[];
      
      return {
        success: true,
        data: result,
        rowsAffected: result.length
      };
    } catch (error) {
      console.error('Query error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute a query and return first result
   */
  queryOne<T = any>(sql: string, params: any[] = []): QueryResult<T> {
    try {
      const db = this.getDb();
      const stmt = db.prepare(sql);
      const result = stmt.get(...params) as T;
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('QueryOne error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute an insert/update/delete query
   */
  execute(sql: string, params: any[] = []): QueryResult<Database.RunResult> {
    try {
      const db = this.getDb();
      const stmt = db.prepare(sql);
      const result = stmt.run(...params);
      
      return {
        success: true,
        data: result,
        rowsAffected: result.changes
      };
    } catch (error) {
      console.error('Execute error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute multiple queries in a transaction
   */
  transaction(queries: Array<{ sql: string; params?: any[] }>): QueryResult<any> {
    try {
      const db = this.getDb();
      const transaction = db.transaction(() => {
        const results = [];
        for (const query of queries) {
          const stmt = db.prepare(query.sql);
          const result = stmt.run(...(query.params || []));
          results.push(result);
        }
        return results;
      });

      const results = transaction();
      
      return {
        success: true,
        data: results,
        rowsAffected: results.reduce((sum, r) => sum + r.changes, 0)
      };
    } catch (error) {
      console.error('Transaction error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get paginated results
   */
  paginate<T = any>(
    sql: string, 
    params: any[] = [], 
    page: number = 1, 
    limit: number = 50
  ): QueryResult<PaginatedResult<T>> {
    try {
      const offset = (page - 1) * limit;
      
      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM (${sql})`;
      const countResult = this.queryOne<{ total: number }>(countSql, params);
      
      if (!countResult.success || !countResult.data) {
        throw new Error('Failed to get total count');
      }

      const total = countResult.data.total;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data
      const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
      const dataResult = this.query<T>(paginatedSql, [...params, limit, offset]);

      if (!dataResult.success) {
        throw new Error(dataResult.error);
      }

      return {
        success: true,
        data: {
          data: dataResult.data || [],
          total,
          page,
          limit,
          totalPages
        }
      };
    } catch (error) {
      console.error('Pagination error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Build WHERE clause from search filters
   */
  buildWhereClause(filters: SearchFilters): { where: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    if (filters.query) {
      conditions.push('(name LIKE ? OR name_hindi LIKE ? OR description LIKE ?)');
      const searchTerm = `%${filters.query}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    if (filters.category) {
      conditions.push('category_id = ?');
      params.push(filters.category);
    }

    if (filters.artist) {
      conditions.push('artist_id = ?');
      params.push(filters.artist);
    }

    if (filters.priceMin !== undefined) {
      conditions.push('selling_price >= ?');
      params.push(filters.priceMin);
    }

    if (filters.priceMax !== undefined) {
      conditions.push('selling_price <= ?');
      params.push(filters.priceMax);
    }

    if (filters.isActive !== undefined) {
      conditions.push('is_active = ?');
      params.push(filters.isActive ? 1 : 0);
    }

    if (filters.dateFrom) {
      conditions.push('created_at >= ?');
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      conditions.push('created_at <= ?');
      params.push(filters.dateTo);
    }

    const where = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { where, params };
  }

  /**
   * Build ORDER BY clause from sort options
   */
  buildOrderClause(sort?: SortOptions): string {
    if (!sort) {
      return 'ORDER BY created_at DESC';
    }

    const direction = sort.direction.toUpperCase();
    return `ORDER BY ${sort.field} ${direction}`;
  }

  /**
   * Close database connection
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
      console.log('✅ Database connection closed');
    }
  }

  /**
   * Get database statistics
   */
  getStats(): QueryResult<any> {
    try {
      const stats = {
        users: this.queryOne('SELECT COUNT(*) as count FROM users').data?.count || 0,
        categories: this.queryOne('SELECT COUNT(*) as count FROM categories').data?.count || 0,
        artists: this.queryOne('SELECT COUNT(*) as count FROM artists').data?.count || 0,
        products: this.queryOne('SELECT COUNT(*) as count FROM products').data?.count || 0,
        customers: this.queryOne('SELECT COUNT(*) as count FROM customers').data?.count || 0,
        orders: this.queryOne('SELECT COUNT(*) as count FROM orders').data?.count || 0,
        dbSize: this.getDbSize()
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get database file size
   */
  private getDbSize(): string {
    try {
      const stats = fs.statSync(this.dbPath);
      const sizeInBytes = stats.size;
      const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);
      return `${sizeInMB} MB`;
    } catch {
      return 'Unknown';
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
